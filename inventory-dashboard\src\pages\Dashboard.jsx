import { useState, useEffect } from 'react';
import { 
  Package, 
  Users, 
  Warehouse, 
  TrendingUp, 
  ShoppingCart, 
  DollarSign,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import KPICard from '../components/Dashboard/KPICard';
import SalesChart from '../components/Dashboard/SalesChart';
import { dashboardService } from '../services/dashboardService';

const Dashboard = () => {
  const [kpis, setKpis] = useState(null);
  const [salesTrend, setSalesTrend] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch KPIs
      const kpisResponse = await dashboardService.getKPIs();
      if (kpisResponse.success) {
        setKpis(kpisResponse.data.kpis);
      }

      // Fetch sales trend
      try {
        const salesResponse = await dashboardService.getSalesTrend();
        if (salesResponse.success) {
          setSalesTrend(salesResponse.data.trend || []);
        }
      } catch (salesError) {
        console.warn('Sales trend data not available:', salesError);
      }

      // Fetch top products
      try {
        const productsResponse = await dashboardService.getTopProducts();
        if (productsResponse.success) {
          setTopProducts(productsResponse.data.products || []);
        }
      } catch (productsError) {
        console.warn('Top products data not available:', productsError);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('حدث خطأ في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        <span className="mr-3 text-gray-600">جاري تحميل البيانات...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-600 ml-2" />
          <span className="text-red-800">{error}</span>
        </div>
        <button 
          onClick={fetchDashboardData}
          className="mt-3 btn-primary text-sm"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">نظرة شاملة على أداء نظام إدارة المخزون</p>
        </div>
        <button 
          onClick={fetchDashboardData}
          className="btn-primary flex items-center space-x-2 space-x-reverse"
        >
          <TrendingUp className="w-4 h-4" />
          <span>تحديث البيانات</span>
        </button>
      </div>

      {/* KPI Cards */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <KPICard
            title="إجمالي المنتجات"
            value={kpis.inventory?.totalProducts || 0}
            icon={Package}
            color="blue"
            subtitle="منتج مسجل في النظام"
          />
          <KPICard
            title="إجمالي الموردين"
            value={kpis.suppliers?.totalSuppliers || 0}
            icon={Users}
            color="green"
            subtitle="مورد نشط"
          />
          <KPICard
            title="المستودعات"
            value={kpis.inventory?.totalWarehouses || 0}
            icon={Warehouse}
            color="purple"
            subtitle="مستودع متاح"
          />
          <KPICard
            title="المنتجات المنخفضة"
            value={kpis.inventory?.lowStockProducts || 0}
            icon={AlertTriangle}
            color="red"
            subtitle="منتج تحت الحد الأدنى"
          />
        </div>
      )}

      {/* Second Row KPIs */}
      {kpis && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <KPICard
            title="أوامر الشراء النشطة"
            value={kpis.orders?.purchase?.active || 0}
            icon={ShoppingCart}
            color="indigo"
            subtitle="أمر شراء قيد التنفيذ"
          />
          <KPICard
            title="أوامر البيع النشطة"
            value={kpis.orders?.sales?.active || 0}
            icon={DollarSign}
            color="yellow"
            subtitle="أمر بيع قيد التنفيذ"
          />
          <KPICard
            title="إجمالي المبيعات"
            value={`${(kpis.orders?.sales?.totalValue || 0).toLocaleString()} ريال`}
            icon={BarChart3}
            color="green"
            subtitle="قيمة المبيعات الإجمالية"
          />
          <KPICard
            title="المنتجات النافدة"
            value={kpis.inventory?.outOfStockProducts || 0}
            icon={AlertTriangle}
            color="red"
            subtitle="منتج نافد من المخزون"
          />
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <SalesChart data={salesTrend} />

        {/* Top Products */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">أفضل المنتجات مبيعاً</h3>
          {topProducts.length > 0 ? (
            <div className="space-y-4">
              {topProducts.slice(0, 5).map((product, index) => (
                <div key={product.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-semibold text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm text-gray-500">كود: {product.code}</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-gray-900">{product.total_sold || 0}</p>
                    <p className="text-sm text-gray-500">قطعة مباعة</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">لا توجد بيانات مبيعات متاحة</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Package className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <span className="text-sm font-medium text-gray-900">إضافة منتج</span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Users className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <span className="text-sm font-medium text-gray-900">إضافة مورد</span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <ShoppingCart className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <span className="text-sm font-medium text-gray-900">أمر شراء جديد</span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <BarChart3 className="w-8 h-8 text-orange-600 mx-auto mb-2" />
            <span className="text-sm font-medium text-gray-900">عرض التقارير</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
